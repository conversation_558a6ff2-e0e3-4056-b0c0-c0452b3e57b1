---
import BurgerButton from "./BurgerBotton.astro";

interface Props {
	navItems: { href: string; label: string }[];
}

const { navItems } = Astro.props;

let base = import.meta.env.BASE_URL;
if (base === "/") {
	base = "";
}
---

<header
	class="bg-primary-50 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300"
>
	<div class="container mx-auto px-4 py-4 flex justify-between items-center">
		<a href="/">
			<div class="flex items-center space-x-2">
				<img
					src={`${base}/logo_podial_1.png`}
					alt="Podial"
					class="w-15 h-15"
				/>
				<img
					src={`${base}/logo_podial_texto_1.png`}
					alt="Podial"
					class="h-15"
				/>
			</div>
		</a>

		<div class="flex justify-end items-center gap-4">
			<!-- Navegación desktop -->
			<nav class="hidden md:flex space-x-6">
				{
					navItems.map((item) => (
						<a
							href={item.href}
							class="text-gray-700 hover:text-primary-600 transition-colors font-medium"
						>
							{item.label}
						</a>
					))
				}
			</nav>

			<!-- Botón pedir cita desktop -->
			<button
				data-modal-target="citaModal"
				class="hidden md:block bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
			>
				Pedir Cita
			</button>

			<!-- Botón hamburguesa móvil -->
			<BurgerButton targetDrawer="mobile-drawer" />
		</div>
	</div>
</header>
