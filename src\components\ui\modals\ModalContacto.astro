---
import ModalBase from "./ModalBase.astro";

export interface Props {
	id: string;
	title?: string;
}

const { id, title = "Pide tu cita" } = Astro.props;
---

<ModalBase id={id} title={title} size="md">
	<div class="space-y-4">
		<!-- Teléfono -->
		<a
			href="tel:+34959407824"
			class="flex items-center justify-between bg-primary-50 border border-primary-200 rounded-xl px-4 py-3 hover:bg-primary-100 transition group"
		>
			<div>
				<p class="text-sm text-gray-500">Teléfono</p>
				<p class="text-lg font-semibold text-gray-800">+34 959 40 78 24</p>
			</div>
			<span
				class="material-symbols-outlined text-primary-600 text-3xl group-hover:scale-110 transition-transform"
			>
				call
			</span>
		</a>

		<!-- Teléfono móvil -->
		<a
			href="tel:+34628284952"
			class="flex items-center justify-between bg-primary-50 border border-primary-200 rounded-xl px-4 py-3 hover:bg-primary-100 transition group"
		>
			<div>
				<p class="text-sm text-gray-500">Teléfono móvil</p>
				<p class="text-lg font-semibold text-gray-800">+34 628 28 49 52</p>
			</div>
			<span
				class="material-symbols-outlined text-primary-600 text-3xl group-hover:scale-110 transition-transform"
			>
				call
			</span>
		</a>

		<!-- WhatsApp -->
		<a
			href="https://wa.me/34628284952"
			target="_blank"
			class="flex items-center justify-between bg-green-50 border border-green-200 rounded-xl px-4 py-3 hover:bg-green-100 transition group"
		>
			<div>
				<p class="text-sm text-gray-500">WhatsApp</p>
				<p class="text-lg font-semibold text-gray-800">+34 628 28 49 52</p>
			</div>
			<span
				class="material-symbols-outlined text-green-600 text-3xl group-hover:scale-110 transition-transform"
			>
				chat
			</span>
		</a>

		<!-- Horarios -->
		<div class="hidden bg-gray-50 rounded-xl p-4 mt-6">
			<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-2"
					>schedule</span
				>
				Horarios de atención
			</h3>
			<div class="space-y-2 text-sm text-gray-600">
				<div class="flex justify-between">
					<span>Lunes - Viernes:</span>
					<span class="font-medium">9:00 - 20:00</span>
				</div>
				<div class="flex justify-between">
					<span>Sábados:</span>
					<span class="font-medium">9:00 - 14:00</span>
				</div>
				<div class="flex justify-between">
					<span>Domingos:</span>
					<span class="font-medium text-red-500">Cerrado</span>
				</div>
			</div>
		</div>
	</div>
</ModalBase>
