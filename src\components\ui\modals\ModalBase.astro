---
export interface Props {
	id: string;
	title?: string;
	size?: "sm" | "md" | "lg" | "xl";
	closeOnBackdrop?: boolean;
}

const { id, title, size = "md", closeOnBackdrop = true } = Astro.props;

const sizeClasses = {
	sm: "max-w-sm",
	md: "max-w-md",
	lg: "max-w-lg",
	xl: "max-w-2xl",
};
---

<!-- Modal Base -->
<div id={id} class="fixed inset-0 z-50 hidden items-center justify-center p-4">
	<!-- Fondo oscuro -->
	<div
		class="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
		data-modal-close={closeOnBackdrop ? "" : undefined}
	>
	</div>

	<!-- Contenedor del modal -->
	<div
		class={`relative bg-white rounded-2xl shadow-xl w-full ${sizeClasses[size]} max-h-[90vh] overflow-hidden z-10 animate-fadeIn`}
	>
		<!-- Header -->
		<div
			class="flex items-center justify-between p-6 border-b border-gray-200"
		>
			{
				title && (
					<h2 class="text-2xl font-title text-primary-600">{title}</h2>
				)
			}
			<button
				class="ml-auto text-gray-400 hover:text-gray-600 transition-colors"
				data-modal-close
				aria-label="Cerrar modal"
			>
				<span class="material-symbols-outlined text-2xl">close</span>
			</button>
		</div>

		<!-- Contenido -->
		<div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
			<slot />
		</div>
	</div>
</div>
