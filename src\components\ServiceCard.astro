---
export interface Props {
	image: string;
	title: string;
	description: string;
	alt: string;
	servicioId: string;
}

const { image, title, description, alt, servicioId } = Astro.props;
---

<div
	class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
	data-modal-target={`modal-${servicioId}`}
>
	<div class="h-48 overflow-hidden">
		<img
			src={image}
			alt={alt}
			class="w-full h-full object-cover transition-transform hover:scale-110 duration-500"
		/>
	</div>
	<div class="p-6">
		<h3 class="font-bold text-xl mb-2 text-gray-800">{title}</h3>
		<p class="text-gray-600 mb-4">{description}</p>
		<button
			data-modal-target={`modal-${servicioId}`}
			class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center transition-colors"
		>
			Ver más detalles <span class="material-symbols-outlined text-sm ml-1"
				>arrow_forward</span
			>
		</button>
	</div>
</div>
